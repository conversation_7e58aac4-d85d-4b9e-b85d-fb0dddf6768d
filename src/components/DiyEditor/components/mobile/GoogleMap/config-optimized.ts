import { DiyComponent } from '@/components/DiyEditor/util'

/** 谷歌地图属性 */
export interface GoogleMapProperty {
  // 标题
  title: string
  // 位置描述
  locationDesc: string
  // 经度坐标
  lng: number
  // 纬度坐标
  lat: number
  // 缩放级别
  zoom: number
  // 地图高度
  height: number
  // 是否显示标题
  showTitle: boolean
  // 是否显示位置描述
  showLocationDesc: boolean
  // 样式设置
  style: {
    // 背景颜色
    bgColor: string
    // 标题颜色
    titleColor: string
    // 位置描述颜色
    descColor: string
    // 圆角
    borderRadius: number
  }
  // 换电站相关配置
  showStations: boolean           // 是否显示换电站
  autoCenter: boolean            // 是否自动居中到站点
  showStationInfo: boolean       // 是否显示站点信息弹窗
  stationRadius: number          // 站点搜索半径(km)
  currentLocation: {             // 当前位置
    lat: number
    lng: number
  }
  // 动态站点图标配置（从配置中读取）
  stationIcons: {
    normal: string               // 正常运营状态的图标URL (status = 1)
    maintenance: string          // 维护中状态的图标URL (status = 2)
    fault: string               // 故障状态的图标URL (status = 3)
  }
  stationIconSize: {             // 图标大小
    width: number
    height: number
  }
  stationIconAnchor: {           // 图标锚点位置（相对于图标中心）
    x: number
    y: number
  }
  // 全屏组件配置
  fullscreenConfig: {
    enabled: boolean             // 是否启用全屏功能
    width: string               // 全屏对话框宽度
    height: string              // 全屏对话框高度
    showControls: boolean       // 是否显示地图控件
  }
  // 地图控件配置
  mapControls: {
    zoomControl: boolean        // 缩放控件
    mapTypeControl: boolean     // 地图类型控件
    streetViewControl: boolean  // 街景控件
    fullscreenControl: boolean  // 原生全屏控件
    rotateControl: boolean      // 旋转控件
    scaleControl: boolean       // 比例尺控件
  }
}

// 定义组件
export const component = {
  id: 'GoogleMap',
  name: '谷歌地图',
  icon: 'mdi:map-marker',
  property: {
    title: '我们的位置',
    locationDesc: '上海市浦东新区陆家嘴',
    lng: 121.5,
    lat: 31.23,
    zoom: 15,
    height: 300,
    showTitle: true,
    showLocationDesc: true,
    style: {
      bgColor: '#ffffff',
      titleColor: '#333333',
      descColor: '#666666',
      borderRadius: 8
    },
    // 换电站默认配置
    showStations: true,
    autoCenter: true,
    showStationInfo: true,
    stationRadius: 10,
    currentLocation: {
      lat: 31.23,
      lng: 121.5
    },
    // 动态站点图标配置
    stationIcons: {
      normal: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png',      // 正常运营 - 绿色
      maintenance: 'https://maps.google.com/mapfiles/ms/icons/yellow-dot.png', // 维护中 - 黄色
      fault: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png'          // 故障 - 红色
    },
    stationIconSize: {
      width: 32,
      height: 32
    },
    stationIconAnchor: {
      x: 16,  // 图标宽度的一半
      y: 32   // 图标高度（图标底部对齐到坐标点）
    },
    // 全屏组件配置
    fullscreenConfig: {
      enabled: true,
      width: '90vw',
      height: '80vh',
      showControls: true
    },
    // 地图控件配置
    mapControls: {
      zoomControl: true,
      mapTypeControl: true,
      streetViewControl: true,    // 启用街景控件
      fullscreenControl: false,   // 禁用原生全屏（使用自定义）
      rotateControl: true,
      scaleControl: true
    }
  }
} as DiyComponent<GoogleMapProperty>
